🚀 GYMKOD REDİS HIZLI KURULUM REHBERİ
================================================================
Windows Server 2019 için Adım Adım Kopyala-Yapıştır Rehberi

⚠️ ÖNEMLİ: Her adımı sırayla takip edin!
✅ PowerShell'i Administrator olarak açın!

================================================================
ADIM 1: DOCKER ENGINE KURULUMU
================================================================
Aşağıdaki kodu kopyalayıp PowerShell'e yapıştırın:

# ADIM 1: DOCKER ENGINE KURULUMU - TEK SEFERDE ÇALIŞTIR
Write-Host "🚀 Docker Engine kurulumu başlatılıyor..." -ForegroundColor Green

# 1.2 - Windows Features aktifleştir
Write-Host "📦 Windows Containers feature aktifleştiriliyor..." -ForegroundColor Yellow
Enable-WindowsOptionalFeature -Online -FeatureName containers -All -NoRestart

Write-Host "🔧 Hyper-V aktifleştiriliyor..." -ForegroundColor Yellow
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All -NoRestart

# 1.3 - Docker Engine kurulumu
Write-Host "📥 Docker Provider yükleniyor..." -ForegroundColor Yellow
Install-Module -Name DockerMsftProvider -Repository PSGallery -Force

Write-Host "🐳 Docker Engine yükleniyor..." -ForegroundColor Yellow
Install-Package -Name docker -ProviderName DockerMsftProvider -Force

Write-Host "🚀 Docker Service başlatılıyor..." -ForegroundColor Yellow
Start-Service Docker
Set-Service -Name Docker -StartupType Automatic

Write-Host "✅ Docker Engine kurulumu tamamlandı!" -ForegroundColor Green
Write-Host "⚠️ Sunucuyu yeniden başlatmanız gerekiyor!" -ForegroundColor Red
Write-Host "💡 Restart-Computer komutunu çalıştırın" -ForegroundColor Cyan

# RESTART SONRASI DOĞRULAMA KOMUTLARI:
# docker version
# docker info  
# docker run hello-world

⏳ RESTART YAPIP ADIM 2'YE GEÇİN!

================================================================
ADIM 2: REDIS CONFIGURATION (RESTART SONRASI)
================================================================
Restart sonrası aşağıdaki kodu kopyalayıp yapıştırın:

# ADIM 2: REDIS CONFIGURATION - TEK SEFERDE ÇALIŞTIR
Write-Host "📁 Redis klasör yapısı oluşturuluyor..." -ForegroundColor Green

# 2.1 - Proje klasörü oluştur
mkdir C:\GymKod\Redis -Force
cd C:\GymKod\Redis
mkdir data -Force
mkdir logs -Force  
mkdir config -Force
mkdir backup -Force
mkdir scripts -Force

Write-Host "✅ Klasör yapısı oluşturuldu!" -ForegroundColor Green

# 2.2 - Production redis.conf oluştur
Write-Host "⚙️ Redis configuration dosyası oluşturuluyor..." -ForegroundColor Yellow

================================================================
ADIM 2.2: REDIS CONFIG DOSYASI
================================================================
Aşağıdaki kodu ayrı bir blok olarak kopyalayıp yapıştırın:

# REDIS.CONF DOSYASI OLUŞTUR
$redisConfig = @"
# Redis Production Configuration for GymKod
# Windows Server 2019 - 4GB RAM, 2 CPU

# Network
bind 0.0.0.0
port 6379
protected-mode yes
tcp-keepalive 300
timeout 300

# Authentication
requirepass GymKodProd2024Redis!Secure

# Memory Management (4GB RAM için optimize)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence (Production için güçlendirilmiş)
save 900 1
save 300 10
save 60 10000

# AOF Persistence
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Performance Tuning
tcp-backlog 511
databases 16

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_b835729c9c13a228"

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128
"@

$redisConfig | Out-File -FilePath "C:\GymKod\Redis\config\redis.conf" -Encoding UTF8
Write-Host "✅ Redis configuration dosyası oluşturuldu!" -ForegroundColor Green

================================================================
ADIM 2.3: DOCKER COMPOSE DOSYASI
================================================================
Aşağıdaki kodu kopyalayıp yapıştırın:

# DOCKER-COMPOSE.PROD.YML DOSYASI OLUŞTUR
$dockerCompose = @"
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - C:\GymKod\Redis\data:/data
      - C:\GymKod\Redis\config\redis.conf:/usr/local/etc/redis/redis.conf
      - C:\GymKod\Redis\logs:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=GymKodProd2024Redis!Secure
    networks:
      - gymkod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "GymKodProd2024Redis!Secure", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.5'

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge
"@

$dockerCompose | Out-File -FilePath "C:\GymKod\Redis\docker-compose.prod.yml" -Encoding UTF8
Write-Host "✅ Docker Compose dosyası oluşturuldu!" -ForegroundColor Green

================================================================
ADIM 3: REDIS CONTAINER BAŞLATMA
================================================================
Aşağıdaki kodu kopyalayıp yapıştırın:

# ADIM 3: REDIS CONTAINER DEPLOYMENT - TEK SEFERDE ÇALIŞTIR
Write-Host "🚀 Redis container başlatılıyor..." -ForegroundColor Green

# 3.1 - Redis container başlat
cd C:\GymKod\Redis
docker-compose -f docker-compose.prod.yml up -d

Write-Host "⏳ Container'ın başlaması bekleniyor..." -ForegroundColor Yellow
Start-Sleep 15

# Container durumunu kontrol et
Write-Host "📊 Container durumu:" -ForegroundColor Cyan
docker ps | findstr gymkod-redis

# Redis loglarını kontrol et
Write-Host "📋 Redis logları:" -ForegroundColor Cyan
docker logs gymkod-redis-prod --tail 10

Write-Host "✅ Redis container deployment tamamlandı!" -ForegroundColor Green

================================================================
ADIM 3.2: REDIS BAĞLANTI TESTİ
================================================================
Aşağıdaki kodu kopyalayıp yapıştırın:

# REDIS BAĞLANTI VE TEMEL TEST
Write-Host "🔍 Redis bağlantı testi yapılıyor..." -ForegroundColor Green

# Ping testi
$pingResult = docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure ping
Write-Host "Ping sonucu: $pingResult" -ForegroundColor Cyan

# Test verisi kaydet ve oku
docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure set test "Hello GymKod"
$testResult = docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure get test
Write-Host "Test verisi: $testResult" -ForegroundColor Cyan

# Memory bilgisi
Write-Host "💾 Memory bilgisi:" -ForegroundColor Cyan
docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure info memory | Select-String "used_memory_human"

Write-Host "✅ Redis bağlantı testi başarılı!" -ForegroundColor Green

================================================================
ADIM 4: GÜVENLİK VE FIREWALL
================================================================
Aşağıdaki kodu kopyalayıp yapıştırın:

# ADIM 4: FIREWALL VE GÜVENLİK - TEK SEFERDE ÇALIŞTIR
Write-Host "🔥 Firewall kuralları ekleniyor..." -ForegroundColor Green

# 4.1 - Firewall kuralları
try {
    # Redis port'unu aç (sadece local network)
    New-NetFirewallRule -DisplayName "GymKod Redis Server" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -RemoteAddress LocalSubnet -ErrorAction Stop
    Write-Host "✅ Redis firewall kuralı eklendi" -ForegroundColor Green
    
    # Docker için gerekli portlar
    New-NetFirewallRule -DisplayName "GymKod Docker API" -Direction Inbound -Protocol TCP -LocalPort 2375,2376 -Action Allow -RemoteAddress LocalSubnet -ErrorAction Stop
    Write-Host "✅ Docker firewall kuralları eklendi" -ForegroundColor Green
}
catch {
    Write-Warning "⚠️ Firewall kuralı eklenemedi: $_"
}

# 4.2 - Güvenlik sertleştirme
Write-Host "🔒 Klasör izinleri ayarlanıyor..." -ForegroundColor Yellow

try {
    # Redis klasör izinleri
    icacls "C:\GymKod\Redis" /grant "Administrators:(OI)(CI)F" /T /Q
    icacls "C:\GymKod\Redis" /remove "Users" /T /Q
    
    # Log dosyası izinleri
    icacls "C:\GymKod\Redis\logs" /grant "Everyone:(OI)(CI)W" /T /Q
    
    Write-Host "✅ Klasör izinleri ayarlandı" -ForegroundColor Green
}
catch {
    Write-Warning "⚠️ Klasör izinleri ayarlanamadı: $_"
}

Write-Host "✅ Güvenlik ayarları tamamlandı!" -ForegroundColor Green

================================================================
SONRAKI ADIMLAR İÇİN: Windows_Server_Redis_Kurulum_Rehberi.txt
================================================================

✅ TEMEL KURULUM TAMAMLANDI!
🔄 Devam etmek için ana rehberdeki Adım 5-7'yi takip edin
📋 Backup scripts, scheduled tasks ve .NET connection için

🎯 .NET CONNECTION STRING:
localhost:6379,password=GymKodProd2024Redis!Secure,abortConnect=false,connectTimeout=10000,syncTimeout=10000,connectRetry=3,keepAlive=60,ssl=false
