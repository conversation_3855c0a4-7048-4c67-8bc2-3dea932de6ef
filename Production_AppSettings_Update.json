{"ConnectionStrings": {"dev": "Server=localhost;Database=GymKodDev;Trusted_Connection=true;TrustServerCertificate=true;", "staging": "Server=your-staging-server;Database=GymKodStaging;User Id=gymkod_user;Password=your-staging-password;TrustServerCertificate=true;", "canlı": "Server=your-production-server;Database=GymKodProd;User Id=gymkod_prod;Password=your-production-password;TrustServerCertificate=true;Encrypt=true;"}, "Redis": {"dev": "localhost:6379,password=GymKod2024Redis!,abortConnect=false,connectTimeout=5000,syncTimeout=5000", "staging": "localhost:6379,password=GymKod2024Redis!,abortConnect=false,connectTimeout=10000,syncTimeout=10000", "canlı": "localhost:6379,password=GymKodProd2024Redis!Secure,abortConnect=false,connectTimeout=10000,syncTimeout=10000,connectRetry=3,keepAlive=60,ssl=false,defaultDatabase=0"}, "AllowedOrigins": {"dev": ["*"], "staging": ["https://staging.gymkod.com"], "canlı": ["https://admin.gymkod.com"]}, "TokenOptions": {"dev": {"Audience": "www.gymkod.com", "Issuer": "www.gymkod.com", "AccessTokenExpiration": 60, "SecurityKey": "your-dev-security-key-min-32-chars"}, "staging": {"Audience": "staging.gymkod.com", "Issuer": "staging.gymkod.com", "AccessTokenExpiration": 30, "SecurityKey": "your-staging-security-key-min-32-chars"}, "canlı": {"Audience": "admin.gymkod.com", "Issuer": "admin.gymkod.com", "AccessTokenExpiration": 15, "SecurityKey": "your-production-security-key-min-64-chars-very-secure"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "StackExchange.Redis": "Warning"}}, "Cache": {"DefaultExpiration": 1800, "MaxMemoryUsage": "2GB", "EnableCompression": true, "EnableEncryption": false, "CircuitBreakerThreshold": 5, "CircuitBreakerTimeout": 30}, "Performance": {"EnableCaching": true, "EnablePerformanceLogging": true, "SlowQueryThreshold": 1000, "MaxConcurrentConnections": 100}}