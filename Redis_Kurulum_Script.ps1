# GymKod Redis Production Kurulum Script
# Windows Server 2019 için otomatik kurulum
# Yazar: Augment Agent
# Tarih: 2025-01-08

param(
    [switch]$SkipDockerInstall,
    [switch]$SkipFirewall,
    [string]$RedisPassword = "GymKodProd2024Redis!Secure"
)

Write-Host "🚀 GymKod Redis Production Kurulum Başlatılıyor..." -ForegroundColor Green
Write-Host "📋 Sistem: Windows Server 2019" -ForegroundColor Yellow
Write-Host "🎯 Hedef: Production Redis Container" -ForegroundColor Yellow

# Admin kontrolü
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "❌ Bu script Administrator olar<PERSON>ırılmalıdır!"
    exit 1
}

# Adım 1: Docker Engine Kurulumu
if (-not $SkipDockerInstall) {
    Write-Host "🔧 Docker Engine kuruluyor..." -ForegroundColor Blue
    
    try {
        # Containers feature aktifle<PERSON>tir
        Write-Host "   📦 Windows Containers feature aktifleştiriliyor..."
        Enable-WindowsOptionalFeature -Online -FeatureName containers -All -NoRestart
        
        # Docker Provider yükle
        Write-Host "   📥 Docker Provider yükleniyor..."
        Install-Module -Name DockerMsftProvider -Repository PSGallery -Force
        
        # Docker Engine yükle
        Write-Host "   🐳 Docker Engine yükleniyor..."
        Install-Package -Name docker -ProviderName DockerMsftProvider -Force
        
        # Docker Service başlat
        Write-Host "   🚀 Docker Service başlatılıyor..."
        Start-Service Docker
        Set-Service -Name Docker -StartupType Automatic
        
        Write-Host "✅ Docker Engine kurulumu tamamlandı!" -ForegroundColor Green
    }
    catch {
        Write-Error "❌ Docker kurulumu başarısız: $_"
        exit 1
    }
}

# Adım 2: Klasör yapısı oluştur
Write-Host "📁 Klasör yapısı oluşturuluyor..." -ForegroundColor Blue

$baseDir = "C:\GymKod\Redis"
$directories = @("data", "logs", "config", "backup", "scripts")

try {
    if (!(Test-Path $baseDir)) {
        New-Item -ItemType Directory -Path $baseDir -Force
    }
    
    foreach ($dir in $directories) {
        $fullPath = Join-Path $baseDir $dir
        if (!(Test-Path $fullPath)) {
            New-Item -ItemType Directory -Path $fullPath -Force
        }
    }
    
    Write-Host "✅ Klasör yapısı oluşturuldu!" -ForegroundColor Green
}
catch {
    Write-Error "❌ Klasör oluşturma hatası: $_"
    exit 1
}

# Adım 3: Redis Configuration dosyası oluştur
Write-Host "⚙️ Redis configuration oluşturuluyor..." -ForegroundColor Blue

$redisConfig = @"
# Redis Production Configuration for GymKod
# Windows Server 2019 - 4GB RAM, 2 CPU

# Network
bind 0.0.0.0
port 6379
protected-mode yes
tcp-keepalive 300
timeout 300

# Authentication
requirepass $RedisPassword

# Memory Management (4GB RAM için optimize)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence (Production için güçlendirilmiş)
save 900 1
save 300 10
save 60 10000

# AOF Persistence
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Performance Tuning
tcp-backlog 511
databases 16

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_b835729c9c13a228"

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128
"@

try {
    $configPath = Join-Path $baseDir "config\redis.conf"
    $redisConfig | Out-File -FilePath $configPath -Encoding UTF8
    Write-Host "✅ Redis configuration oluşturuldu!" -ForegroundColor Green
}
catch {
    Write-Error "❌ Redis config oluşturma hatası: $_"
    exit 1
}

# Adım 4: Docker Compose dosyası oluştur
Write-Host "🐳 Docker Compose dosyası oluşturuluyor..." -ForegroundColor Blue

$dockerCompose = @"
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - C:\GymKod\Redis\data:/data
      - C:\GymKod\Redis\config\redis.conf:/usr/local/etc/redis/redis.conf
      - C:\GymKod\Redis\logs:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=$RedisPassword
    networks:
      - gymkod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "$RedisPassword", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.5'

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge
"@

try {
    $composePath = Join-Path $baseDir "docker-compose.prod.yml"
    $dockerCompose | Out-File -FilePath $composePath -Encoding UTF8
    Write-Host "✅ Docker Compose dosyası oluşturuldu!" -ForegroundColor Green
}
catch {
    Write-Error "❌ Docker Compose oluşturma hatası: $_"
    exit 1
}

# Adım 5: Utility Scripts oluştur
Write-Host "📜 Utility scripts oluşturuluyor..." -ForegroundColor Blue

# Health Check Script
$healthCheckScript = @"
# Redis Health Check Script
`$redisPassword = "$RedisPassword"
`$containerName = "gymkod-redis-prod"

try {
    `$result = docker exec `$containerName redis-cli -a `$redisPassword ping
    if (`$result -eq "PONG") {
        Write-Host "✅ Redis is healthy" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "❌ Redis ping failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Redis connection error: `$_" -ForegroundColor Red
    exit 1
}
"@

# Backup Script
$backupScript = @"
# Redis Backup Script
`$backupDir = "C:\GymKod\Redis\backup"
`$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
`$backupFile = "`$backupDir\redis_backup_`$timestamp.rdb"

# Backup klasörü oluştur
if (!(Test-Path `$backupDir)) {
    New-Item -ItemType Directory -Path `$backupDir
}

# Redis BGSAVE komutu
docker exec gymkod-redis-prod redis-cli -a $RedisPassword BGSAVE

# RDB dosyasını kopyala
docker cp gymkod-redis-prod:/data/dump.rdb `$backupFile

Write-Host "✅ Backup completed: `$backupFile"

# 7 günden eski backup'ları sil
Get-ChildItem `$backupDir -Filter "*.rdb" | Where-Object {`$_.CreationTime -lt (Get-Date).AddDays(-7)} | Remove-Item
"@

try {
    $healthCheckScript | Out-File -FilePath (Join-Path $baseDir "scripts\health_check.ps1") -Encoding UTF8
    $backupScript | Out-File -FilePath (Join-Path $baseDir "scripts\backup.ps1") -Encoding UTF8
    Write-Host "✅ Utility scripts oluşturuldu!" -ForegroundColor Green
}
catch {
    Write-Error "❌ Script oluşturma hatası: $_"
    exit 1
}

# Adım 6: Firewall kuralları
if (-not $SkipFirewall) {
    Write-Host "🔥 Firewall kuralları ekleniyor..." -ForegroundColor Blue
    
    try {
        New-NetFirewallRule -DisplayName "Redis Server" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -RemoteAddress LocalSubnet -ErrorAction SilentlyContinue
        Write-Host "✅ Firewall kuralları eklendi!" -ForegroundColor Green
    }
    catch {
        Write-Warning "⚠️ Firewall kuralı eklenemedi: $_"
    }
}

# Adım 7: Redis Container başlat
Write-Host "🚀 Redis container başlatılıyor..." -ForegroundColor Blue

try {
    Set-Location $baseDir
    docker-compose -f docker-compose.prod.yml up -d
    
    # Container'ın başlamasını bekle
    Start-Sleep 10
    
    # Health check
    $healthResult = docker exec gymkod-redis-prod redis-cli -a $RedisPassword ping 2>$null
    if ($healthResult -eq "PONG") {
        Write-Host "✅ Redis container başarıyla başlatıldı!" -ForegroundColor Green
    } else {
        Write-Warning "⚠️ Redis container başlatıldı ama health check başarısız!"
    }
}
catch {
    Write-Error "❌ Container başlatma hatası: $_"
    exit 1
}

# Adım 8: Scheduled Tasks oluştur
Write-Host "⏰ Scheduled tasks oluşturuluyor..." -ForegroundColor Blue

try {
    # Redis otomatik başlatma
    $action = New-ScheduledTaskAction -Execute "docker-compose" -Argument "-f C:\GymKod\Redis\docker-compose.prod.yml up -d" -WorkingDirectory "C:\GymKod\Redis"
    $trigger = New-ScheduledTaskTrigger -AtStartup
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
    
    Register-ScheduledTask -TaskName "GymKod Redis Startup" -Action $action -Trigger $trigger -Settings $settings -User "SYSTEM" -Force
    
    # Günlük backup
    $backupAction = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GymKod\Redis\scripts\backup.ps1"
    $backupTrigger = New-ScheduledTaskTrigger -Daily -At "02:00"
    
    Register-ScheduledTask -TaskName "GymKod Redis Backup" -Action $backupAction -Trigger $backupTrigger -User "SYSTEM" -Force
    
    Write-Host "✅ Scheduled tasks oluşturuldu!" -ForegroundColor Green
}
catch {
    Write-Warning "⚠️ Scheduled task oluşturma hatası: $_"
}

# Kurulum özeti
Write-Host ""
Write-Host "🎉 GymKod Redis Production Kurulumu Tamamlandı!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host "📍 Redis Container: gymkod-redis-prod" -ForegroundColor White
Write-Host "🔑 Password: $RedisPassword" -ForegroundColor White
Write-Host "📂 Base Directory: $baseDir" -ForegroundColor White
Write-Host "🔗 Connection: localhost:6379" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Yönetim Komutları:" -ForegroundColor Yellow
Write-Host "   Health Check: C:\GymKod\Redis\scripts\health_check.ps1" -ForegroundColor White
Write-Host "   Backup: C:\GymKod\Redis\scripts\backup.ps1" -ForegroundColor White
Write-Host "   Container Logs: docker logs gymkod-redis-prod" -ForegroundColor White
Write-Host "   Redis CLI: docker exec -it gymkod-redis-prod redis-cli -a $RedisPassword" -ForegroundColor White
Write-Host ""
Write-Host "✅ Sistem hazır! .NET uygulamanızı test edebilirsiniz." -ForegroundColor Green
