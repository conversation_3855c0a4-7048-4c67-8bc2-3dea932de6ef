🎯 SIFIRDAN WINDOWS SERVER 2019 REDİS KURULUM PROMPT
================================================================

PROJE: GymKod Pro - Multi-tenant Spor Salonu Yönetim Sistemi
HEDEF: Windows Server 2019'da Production Redis Cache Altyapısı
SUNUCU: 4GB RAM, 2 CPU, 1 salon 100 üye (ileride ölçeklenecek)
YAKLAŞIM: Sıfırdan → Production Ready → Test → Monitoring

================================================================
MEVCUT PROJE ANALİZİ VE DURUM
================================================================

✅ TAMAMLANAN (LOCAL DEVELOPMENT):
1. Redis Cache Service (RedisCacheService.cs) - StackExchange.Redis ile
2. Multi-tenant Cache Key System (CacheKeyHelper.cs) - "gym:{companyId}:{entity}:{action}" pattern
3. AOP Cache Aspect (CacheAspect.cs) - Castle.DynamicProxy ile method-level caching
4. Company Context (CompanyContext.cs) - JWT'den CompanyID extraction
5. Cache Interface (ICacheService.cs) - Generic type support ile
6. Manager Integration - MemberManager, PaymentManager vb. cache aspects
7. Local Docker Desktop + Redis Container - Development ortamında çalışıyor

🔧 MEVCUT CACHE ALTYAPISI:
- Multi-tenant isolation: ✅ (gym:1:member:details vs gym:2:member:details)
- JSON Serialization: ✅ (TypeNameHandling.Auto ile)
- Error Handling: ✅ (Fallback mechanism)
- Async Support: ✅ (GetAsync, SetAsync)
- Cache Invalidation: ✅ (RemoveByPattern)
- AOP Integration: ✅ ([CacheAspect(300)] attribute'ları)

📊 CACHE KEY PATTERN ÖRNEKLERİ:
- gym:1:member:getmemberdetails:hash123
- gym:1:membershiptype:getall:noparams
- gym:2:payment:getmemberpayments:member456
- gym:1:license:getpackages:noparams

⚠️ EKSİK OLAN (PRODUCTION DEPLOYMENT):
- Windows Server 2019'da Redis kurulumu
- Production Redis configuration
- Security hardening
- Monitoring ve backup
- Auto-restart configuration
- Performance optimization

================================================================
PROMPT: WINDOWS SERVER 2019 REDİS PRODUCTION KURULUMU
================================================================

AMAÇ: 
Mevcut GymKod Pro projesinin cache altyapısını Windows Server 2019'a deploy etmek.
Local'de çalışan Redis cache sistemini production sunucusuna taşımak.

SUNUCU BİLGİLERİ:
- OS: Windows Server 2019
- RAM: 4GB (Redis için 2GB ayrılacak)
- CPU: 2 Core
- Disk: Yeterli alan mevcut
- Network: Stabil bağlantı
- Kullanım: 1 salon, 100 üye (başlangıç)

SORUN:
- Windows Server 2019'da Docker Desktop kurulamıyor
- Redis kurulumu hiç yapılmamış
- Production configuration bilgisi yok
- Security ve monitoring setup eksik
- Backup strategy yok
- Auto-restart mechanism yok

İSTEDİĞİMİZ:
1. Windows Server 2019'a uygun Redis kurulum yöntemi belirleme
2. Docker Engine (Desktop değil) kurulumu VEYA Windows native Redis
3. Production-ready Redis configuration
4. Security hardening (firewall, authentication, encryption)
5. Monitoring ve health check setup
6. Automated backup system
7. Auto-restart ve service management
8. .NET uygulaması connection string update
9. Test ve validation procedures
10. Troubleshooting guide

KAPSAM:
- Docker Engine kurulumu (Windows Server 2019 uyumlu)
- Redis container deployment (production config ile)
- Windows Firewall configuration
- Redis authentication ve security
- Backup automation scripts
- Health monitoring setup
- Service management (Windows Service olarak)
- Performance tuning (4GB RAM için optimize)
- Connection string production update
- Test scenarios ve validation

BEKLENEN ÇIKTILAR:
1. Adım adım kurulum rehberi (.txt dosyası)
2. PowerShell script'leri (tek seferde çalıştırılabilir)
3. Redis production configuration dosyası
4. Backup ve monitoring script'leri
5. Troubleshooting guide
6. Performance optimization settings
7. Security checklist
8. Test validation procedures

TEKNIK GEREKSINIMLER:
- Redis 7.x (latest stable)
- Password authentication
- Memory limit: 2GB (4GB RAM'in yarısı)
- Persistence: RDB + AOF
- Log level: notice
- Connection timeout: 10 seconds
- Max connections: 1000
- Eviction policy: allkeys-lru

GÜVENLIK GEREKSINIMLERİ:
- Strong password authentication
- Firewall rules (sadece local network)
- Dangerous commands disabled
- Log file protection
- Data directory permissions
- SSL/TLS (opsiyonel, local network için)

MONITORING GEREKSINIMLERİ:
- Redis health check script
- Memory usage monitoring
- Connection count tracking
- Performance metrics
- Error logging
- Backup verification

BACKUP STRATEJİSİ:
- Daily automated backups
- 7 gün retention policy
- RDB snapshot + AOF log
- Backup verification
- Recovery testing

PERFORMANCE HEDEFLERI:
- Response time: <50ms (cache hit)
- Memory usage: <2GB
- CPU usage: <50%
- Uptime: 99.9%
- Cache hit ratio: >80%

TEST SENARYOLARI:
1. Redis service başlatma/durdurma
2. .NET uygulaması bağlantı testi
3. Cache set/get operations
4. Multi-tenant isolation test
5. Backup/restore test
6. Failover test
7. Performance test (100 concurrent users)
8. Memory limit test

ÇIKTI FORMAT:
- Windows_Server_Redis_Kurulum_Rehberi.txt (detaylı adımlar)
- Redis_Kurulum_Script.ps1 (otomatik kurulum)
- Production_AppSettings_Update.json (.NET config)
- Backup_Script.ps1 (otomatik backup)
- Health_Check_Script.ps1 (monitoring)
- Troubleshooting_Guide.txt (sorun giderme)

⚠️ ÖNEMLİ NOTLAR:
1. Windows Server 2019'da Docker Desktop çalışmaz, Docker Engine gerekli
2. Alternatif olarak Windows native Redis kurulumu da değerlendirilebilir
3. Tüm script'ler PowerShell ile yazılmalı
4. Tek seferde kopyala-yapıştır formatında olmalı
5. Her adım test edilebilir olmalı
6. Rollback procedures dahil edilmeli
7. Production environment için optimize edilmeli

BAŞLANGIÇ KONTROLÜ:
- Windows Server 2019 güncel mi?
- Administrator yetkisi var mı?
- Internet bağlantısı stabil mi?
- 4GB RAM kullanılabilir mi?
- Disk alanı yeterli mi? (min 10GB)
- PowerShell 5.1+ kurulu mu?

SONUÇ KONTROLÜ:
- Redis service çalışıyor mu?
- .NET uygulaması bağlanabiliyor mu?
- Cache operations çalışıyor mu?
- Backup script'i çalışıyor mu?
- Health check başarılı mı?
- Performance hedefleri karşılanıyor mu?

Bu prompt'u AI'ya gönderdiğimde, sıfırdan Windows Server 2019'a Redis kurulumu için
eksiksiz bir rehber hazırlamasını bekliyorum. Mevcut cache altyapımız hazır, sadece
production deployment kısmı eksik.

================================================================
ÖRNEK KULLANIM
================================================================

Bu prompt'u yeni bir AI conversation'da şu şekilde kullanın:

"Merhaba! Aşağıdaki prompt'a göre Windows Server 2019'a Redis kurulumu için
eksiksiz bir rehber hazırlar mısın? Tüm adımları PowerShell script'leri ile
birlikte, tek seferde kopyala-yapıştır formatında istiyorum.

[PROMPT İÇERİĞİNİ BURAYA KOPYALA]

Lütfen şu dosyaları oluştur:
1. Windows_Server_Redis_Kurulum_Rehberi.txt - Detaylı adım adım rehber
2. Redis production configuration
3. PowerShell automation scripts
4. Test ve validation procedures
5. Troubleshooting guide

Başlayalım!"

================================================================
PROMPT HAZIR - KOPYALA VE YENİ AI CONVERSATION'DA KULLAN
================================================================
