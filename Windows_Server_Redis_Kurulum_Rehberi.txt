🎯 WINDOWS SERVER 2019 REDİS KURULUM REHBERİ
================================================================
PROJE: GymKod Pro - Production Redis Deployment
SUNUCU: Windows Server 2019 (4GB RAM, 2 CPU)
HEDEF: 1 salon 100 üye → İlerde 1000+ salon 100K+ kullanıcı
YAKLAŞIM: Docker Engine → Redis Container → Production Config

================================================================
ADIM 1: WINDOWS SERVER 2019 DOCKER ENGINE KURULUMU
================================================================

⚠️ ÖNEMLİ: Windows Server 2019'da Docker Desktop KURULAMAZ!
✅ ÇÖZÜM: Docker Engine (Server Edition) kurulacak

1.1 POWERSHELL YÖNETİCİ OLARAK AÇ:
   - Start → PowerShell → Sağ tık → "Run as Administrator"

1.2 WINDOWS FEATURES AKTIFLEŞTIR:
   ```powershell
   # Containers feature'ını aktifleştir
   Enable-WindowsOptionalFeature -Online -FeatureName containers -All
   
   # Hyper-V aktifleştir (gerekirse)
   Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
   
   # Sunucuyu yeniden başlat
   Restart-Computer
   ```

1.3 DOCKER ENGINE KURULUMU:
   ```powershell
   # Docker Provider'ı yükle
   Install-Module -Name DockerMsftProvider -Repository PSGallery -Force
   
   # Docker Engine'i yükle
   Install-Package -Name docker -ProviderName DockerMsftProvider
   
   # Docker Service'i başlat
   Start-Service Docker
   
   # Otomatik başlatma aktif et
   Set-Service -Name Docker -StartupType Automatic
   ```

1.4 DOCKER KURULUM DOĞRULAMA:
   ```powershell
   # Docker version kontrol
   docker version
   
   # Docker info kontrol
   docker info
   
   # Test container çalıştır
   docker run hello-world
   ```

================================================================
ADIM 2: PRODUCTION REDİS CONFIGURATION
================================================================

2.1 PROJE KLASÖRÜ OLUŞTUR:
   ```powershell
   # Ana klasör oluştur
   mkdir C:\GymKod\Redis
   cd C:\GymKod\Redis
   
   # Alt klasörler oluştur
   mkdir data
   mkdir logs
   mkdir config
   mkdir backup
   ```

2.2 PRODUCTION REDIS.CONF OLUŞTUR:
   C:\GymKod\Redis\config\redis.conf dosyası oluştur:

   ```conf
   # Redis Production Configuration for GymKod
   # Windows Server 2019 - 4GB RAM, 2 CPU
   
   # Network
   bind 0.0.0.0
   port 6379
   protected-mode yes
   tcp-keepalive 300
   timeout 300
   
   # Authentication
   requirepass GymKodProd2024Redis!Secure
   
   # Memory Management (4GB RAM için optimize)
   maxmemory 2gb
   maxmemory-policy allkeys-lru
   
   # Persistence (Production için güçlendirilmiş)
   save 900 1
   save 300 10
   save 60 10000
   
   # AOF Persistence
   appendonly yes
   appendfsync everysec
   no-appendfsync-on-rewrite no
   auto-aof-rewrite-percentage 100
   auto-aof-rewrite-min-size 64mb
   
   # Logging
   loglevel notice
   logfile /var/log/redis/redis-server.log
   
   # Performance Tuning
   tcp-backlog 511
   databases 16
   
   # Security
   rename-command FLUSHDB ""
   rename-command FLUSHALL ""
   rename-command DEBUG ""
   rename-command CONFIG "CONFIG_b835729c9c13a228"
   
   # Slow Log
   slowlog-log-slower-than 10000
   slowlog-max-len 128
   ```

2.3 PRODUCTION DOCKER-COMPOSE.YML OLUŞTUR:
   C:\GymKod\Redis\docker-compose.prod.yml dosyası:

   ```yaml
   version: '3.8'
   
   services:
     redis:
       image: redis:7-alpine
       container_name: gymkod-redis-prod
       restart: always
       ports:
         - "6379:6379"
       volumes:
         - C:\GymKod\Redis\data:/data
         - C:\GymKod\Redis\config\redis.conf:/usr/local/etc/redis/redis.conf
         - C:\GymKod\Redis\logs:/var/log/redis
       command: redis-server /usr/local/etc/redis/redis.conf
       environment:
         - REDIS_PASSWORD=GymKodProd2024Redis!Secure
       networks:
         - gymkod-network
       healthcheck:
         test: ["CMD", "redis-cli", "-a", "GymKodProd2024Redis!Secure", "ping"]
         interval: 30s
         timeout: 10s
         retries: 3
         start_period: 30s
       deploy:
         resources:
           limits:
             memory: 2G
             cpus: '1.5'
           reservations:
             memory: 1G
             cpus: '0.5'
   
   volumes:
     redis_data:
       driver: local
   
   networks:
     gymkod-network:
       driver: bridge
   ```

================================================================
ADIM 3: REDİS CONTAINER DEPLOYMENT
================================================================

3.1 REDİS CONTAINER BAŞLAT:
   ```powershell
   cd C:\GymKod\Redis
   
   # Container'ı başlat
   docker-compose -f docker-compose.prod.yml up -d
   
   # Container durumunu kontrol et
   docker ps
   
   # Redis loglarını kontrol et
   docker logs gymkod-redis-prod
   ```

3.2 REDİS BAĞLANTI TESTİ:
   ```powershell
   # Redis CLI ile bağlan
   docker exec -it gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure
   
   # Test komutları
   ping
   set test "Hello GymKod"
   get test
   info memory
   exit
   ```

================================================================
ADIM 4: WINDOWS FIREWALL VE GÜVENLİK
================================================================

4.1 FIREWALL KURALLARI:
   ```powershell
   # Redis port'unu aç (sadece local network)
   New-NetFirewallRule -DisplayName "Redis Server" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -RemoteAddress LocalSubnet
   
   # Docker için gerekli portlar
   New-NetFirewallRule -DisplayName "Docker API" -Direction Inbound -Protocol TCP -LocalPort 2375,2376 -Action Allow -RemoteAddress LocalSubnet
   ```

4.2 GÜVENLİK SERTLEŞTIRME:
   ```powershell
   # Redis klasör izinleri
   icacls "C:\GymKod\Redis" /grant "Administrators:(OI)(CI)F" /T
   icacls "C:\GymKod\Redis" /remove "Users" /T
   
   # Log dosyası izinleri
   icacls "C:\GymKod\Redis\logs" /grant "Everyone:(OI)(CI)W" /T
   ```

================================================================
ADIM 5: MONITORING VE BACKUP SCRIPTLERI
================================================================

5.1 HEALTH CHECK SCRIPT OLUŞTUR:
   C:\GymKod\Redis\scripts\health_check.ps1:

   ```powershell
   # Redis Health Check Script
   $redisPassword = "GymKodProd2024Redis!Secure"
   $containerName = "gymkod-redis-prod"
   
   try {
       $result = docker exec $containerName redis-cli -a $redisPassword ping
       if ($result -eq "PONG") {
           Write-Host "✅ Redis is healthy" -ForegroundColor Green
           exit 0
       } else {
           Write-Host "❌ Redis ping failed" -ForegroundColor Red
           exit 1
       }
   } catch {
       Write-Host "❌ Redis connection error: $_" -ForegroundColor Red
       exit 1
   }
   ```

5.2 BACKUP SCRIPT OLUŞTUR:
   C:\GymKod\Redis\scripts\backup.ps1:

   ```powershell
   # Redis Backup Script
   $backupDir = "C:\GymKod\Redis\backup"
   $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
   $backupFile = "$backupDir\redis_backup_$timestamp.rdb"
   
   # Backup klasörü oluştur
   if (!(Test-Path $backupDir)) {
       New-Item -ItemType Directory -Path $backupDir
   }
   
   # Redis BGSAVE komutu
   docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure BGSAVE
   
   # RDB dosyasını kopyala
   docker cp gymkod-redis-prod:/data/dump.rdb $backupFile
   
   Write-Host "✅ Backup completed: $backupFile"
   
   # 7 günden eski backup'ları sil
   Get-ChildItem $backupDir -Filter "*.rdb" | Where-Object {$_.CreationTime -lt (Get-Date).AddDays(-7)} | Remove-Item
   ```

================================================================
ADIM 6: OTOMATIK BAŞLATMA VE SERVİS YÖNETİMİ
================================================================

6.1 WINDOWS TASK SCHEDULER KURULUMU:
   ```powershell
   # Redis otomatik başlatma task'ı oluştur
   $action = New-ScheduledTaskAction -Execute "docker-compose" -Argument "-f C:\GymKod\Redis\docker-compose.prod.yml up -d" -WorkingDirectory "C:\GymKod\Redis"
   $trigger = New-ScheduledTaskTrigger -AtStartup
   $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
   
   Register-ScheduledTask -TaskName "GymKod Redis Startup" -Action $action -Trigger $trigger -Settings $settings -User "SYSTEM"
   ```

6.2 GÜNLÜK BACKUP TASK'I:
   ```powershell
   # Günlük backup task'ı
   $backupAction = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GymKod\Redis\scripts\backup.ps1"
   $backupTrigger = New-ScheduledTaskTrigger -Daily -At "02:00"
   
   Register-ScheduledTask -TaskName "GymKod Redis Backup" -Action $backupAction -Trigger $backupTrigger -User "SYSTEM"
   ```

================================================================
ADIM 7: .NET UYGULAMASI PRODUCTION CONNECTION
================================================================

7.1 APPSETTINGS.JSON GÜNCELLEMESİ:
   Production connection string'i güncelle:

   ```json
   "Redis": {
     "canlı": "localhost:6379,password=GymKodProd2024Redis!Secure,abortConnect=false,connectTimeout=10000,syncTimeout=10000,connectRetry=3,keepAlive=60,ssl=false"
   }
   ```

7.2 CONNECTION TEST:
   .NET uygulamasını çalıştır ve Redis bağlantısını test et.

================================================================
SONRAKI ADIMLAR (PROMPT 8-10)
================================================================

✅ TAMAMLANAN: Redis Production Kurulumu
🔄 SONRAKI: Performance Optimization ve Load Testing
🔄 SONRAKI: Cache Reliability ve Circuit Breaker
🔄 SONRAKI: Security Hardening ve Final Deployment

Bu rehberi takip ederek Windows Server 2019'da production-ready Redis kurulumu yapabilirsiniz.

================================================================
ADIM 8: TROUBLESHOOTING VE SORUN GİDERME
================================================================

8.1 YAYGIN SORUNLAR VE ÇÖZÜMLERİ:

SORUN 1: Docker Engine kurulumu başarısız
ÇÖZÜM:
```powershell
# Windows Update'leri kontrol et
Get-WindowsUpdate
Install-WindowsUpdate -AcceptAll -AutoReboot

# .NET Framework 4.8 kurulu mu kontrol et
Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release
```

SORUN 2: Container başlatılamıyor
ÇÖZÜM:
```powershell
# Docker service durumunu kontrol et
Get-Service Docker
Restart-Service Docker

# Container loglarını incele
docker logs gymkod-redis-prod --tail 50

# Port kullanımını kontrol et
netstat -an | findstr :6379
```

SORUN 3: Redis bağlantı hatası
ÇÖZÜM:
```powershell
# Redis container içine gir
docker exec -it gymkod-redis-prod sh

# Redis config'i kontrol et
redis-cli -a GymKodProd2024Redis!Secure CONFIG GET "*"

# Memory kullanımını kontrol et
redis-cli -a GymKodProd2024Redis!Secure INFO memory
```

8.2 PERFORMANCE MONİTORİNG:
```powershell
# Redis stats monitoring script
# C:\GymKod\Redis\scripts\monitor.ps1
$password = "GymKodProd2024Redis!Secure"

while ($true) {
    $info = docker exec gymkod-redis-prod redis-cli -a $password INFO stats
    $memory = docker exec gymkod-redis-prod redis-cli -a $password INFO memory

    Write-Host "=== Redis Stats $(Get-Date) ===" -ForegroundColor Yellow
    Write-Host $info
    Write-Host $memory

    Start-Sleep 60
}
```

================================================================
ADIM 9: PRODUCTION DEPLOYMENT CHECKLİST
================================================================

9.1 KURULUM ÖNCESİ KONTROLLER:
□ Windows Server 2019 güncel mi?
□ 4GB RAM mevcut mu?
□ Disk alanı yeterli mi? (min 20GB)
□ Network bağlantısı stabil mi?
□ Firewall kuralları hazır mı?

9.2 KURULUM SONRASI KONTROLLER:
□ Docker Engine çalışıyor mu?
□ Redis container ayakta mı?
□ Redis AUTH çalışıyor mu?
□ Backup script'i çalışıyor mu?
□ Health check çalışıyor mu?
□ .NET app bağlanabiliyor mu?

9.3 GÜVENLİK KONTROLLERİ:
□ Redis password güçlü mü?
□ Firewall kuralları aktif mi?
□ Klasör izinleri doğru mu?
□ Tehlikeli komutlar disable mi?
□ Log dosyaları korunuyor mu?

================================================================
ADIM 10: KAPASITEYI ARTTIRMA PLANI
================================================================

10.1 SUNUCU KAPASITESI ARTTIRMA (İLERDE):
Mevcut: 4GB RAM, 2 CPU → 1 salon 100 üye
Hedef: 16GB RAM, 8 CPU → 100 salon 10K üye

REDIS CONFIG DEĞİŞİKLİKLERİ:
```conf
# 16GB RAM için
maxmemory 8gb
maxmemory-policy allkeys-lru

# Performance tuning
tcp-backlog 2048
databases 32

# Connection limits
maxclients 10000
```

DOCKER COMPOSE DEĞİŞİKLİKLERİ:
```yaml
deploy:
  resources:
    limits:
      memory: 8G
      cpus: '4.0'
    reservations:
      memory: 4G
      cpus: '2.0'
```

10.2 CLUSTER KURULUMU (100+ SALON İÇİN):
```yaml
# Redis Cluster için 3 node
services:
  redis-node-1:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes-6379.conf --cluster-node-timeout 5000 --appendonly yes --port 6379

  redis-node-2:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes-6380.conf --cluster-node-timeout 5000 --appendonly yes --port 6380

  redis-node-3:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes-6381.conf --cluster-node-timeout 5000 --appendonly yes --port 6381
```

================================================================
ADIM 11: MAINTENANCE VE GÜNCELLEME
================================================================

11.1 REDIS GÜNCELLEME PROCEDURE:
```powershell
# 1. Backup al
C:\GymKod\Redis\scripts\backup.ps1

# 2. Container'ı durdur
docker-compose -f C:\GymKod\Redis\docker-compose.prod.yml down

# 3. Image'i güncelle
docker pull redis:7-alpine

# 4. Container'ı yeniden başlat
docker-compose -f C:\GymKod\Redis\docker-compose.prod.yml up -d

# 5. Health check
C:\GymKod\Redis\scripts\health_check.ps1
```

11.2 WEEKLY MAINTENANCE SCRIPT:
```powershell
# C:\GymKod\Redis\scripts\weekly_maintenance.ps1
Write-Host "🔧 Weekly Redis Maintenance Started" -ForegroundColor Blue

# 1. Backup
& "C:\GymKod\Redis\scripts\backup.ps1"

# 2. Log rotation
$logFile = "C:\GymKod\Redis\logs\redis-server.log"
if ((Get-Item $logFile).Length -gt 100MB) {
    Move-Item $logFile "$logFile.old"
    docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure CONFIG SET logfile /var/log/redis/redis-server.log
}

# 3. Memory optimization
docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure MEMORY PURGE

# 4. Health report
& "C:\GymKod\Redis\scripts\health_check.ps1"

Write-Host "✅ Weekly Maintenance Completed" -ForegroundColor Green
```

================================================================
ADIM 12: EMERGENCY RECOVERY PLAN
================================================================

12.1 REDIS CRASH RECOVERY:
```powershell
# 1. Container durumunu kontrol et
docker ps -a | findstr gymkod-redis

# 2. Container restart
docker restart gymkod-redis-prod

# 3. Eğer restart çalışmazsa
docker-compose -f C:\GymKod\Redis\docker-compose.prod.yml down
docker-compose -f C:\GymKod\Redis\docker-compose.prod.yml up -d

# 4. Data corruption varsa backup'tan restore
docker cp C:\GymKod\Redis\backup\redis_backup_YYYYMMDD_HHMMSS.rdb gymkod-redis-prod:/data/dump.rdb
docker restart gymkod-redis-prod
```

12.2 DISASTER RECOVERY:
```powershell
# Tam sistem çökmesi durumunda
# 1. Yeni sunucuda Docker kurulumu
# 2. Redis config dosyalarını kopyala
# 3. Son backup'ı restore et
# 4. .NET uygulamasını yeni sunucuya yönlendir
```

================================================================
SONUÇ VE İLETİŞİM
================================================================

✅ KURULUM TAMAMLANDI:
- Windows Server 2019'da Docker Engine
- Production Redis Container
- Monitoring ve Backup Scripts
- Security Hardening
- Auto-restart Configuration

📞 DESTEK GEREKTİĞİNDE:
1. Redis loglarını kontrol et: docker logs gymkod-redis-prod
2. Health check çalıştır: C:\GymKod\Redis\scripts\health_check.ps1
3. Backup'ları kontrol et: C:\GymKod\Redis\backup\
4. .NET uygulaması connection string'ini kontrol et

🚀 SONRAKI ADIMLAR:
- Prompt 8: Performance Optimization ve Load Testing
- Prompt 9: Cache Reliability ve Circuit Breaker
- Prompt 10: Security Hardening ve Final Deployment

Bu rehber ile Windows Server 2019'da production-ready Redis kurulumu tamamlanmıştır.
