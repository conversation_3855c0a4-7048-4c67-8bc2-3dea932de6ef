🎯 WINDOWS SERVER 2019 REDİS KURULUM REHBERİ
================================================================
PROJE: GymKod Pro - Production Redis Deployment
SUNUCU: Windows Server 2019 (4GB RAM, 2 CPU)
HEDEF: 1 salon 100 üye → İlerde 1000+ salon 100K+ kullanıcı
YAKLAŞIM: Docker Engine → Redis Container → Production Config

================================================================
ADIM 1: WINDOWS SERVER 2019 DOCKER ENGINE KURULUMU
================================================================

⚠️ ÖNEMLİ: Windows Server 2019'da Docker Desktop KURULAMAZ!
✅ ÇÖZÜM: Docker Engine (Server Edition) kurulacak

🔥 ADIM 1 - TEK SEFERDE KOPYALA YAPIŞTIIR:
PowerShell'i Administrator olarak açın ve aşağıdaki kodu tek seferde yapıştırın:

```powershell
# ADIM 1: DOCKER ENGINE KURULUMU - TEK SEFERDE ÇALIŞTIR
Write-Host "🚀 Docker Engine kurulumu başlatılıyor..." -ForegroundColor Green

# 1.2 - Windows Features aktifleştir
Write-Host "📦 Windows Containers feature aktifleştiriliyor..." -ForegroundColor Yellow
Enable-WindowsOptionalFeature -Online -FeatureName containers -All -NoRestart

Write-Host "🔧 Hyper-V aktifleştiriliyor..." -ForegroundColor Yellow
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All -NoRestart

# 1.3 - Docker Engine kurulumu (GÜNCELLENMIŞ - SORUN ÇÖZÜMÜ)
Write-Host "📥 Docker Provider yükleniyor..." -ForegroundColor Yellow
try {
    Install-Module -Name DockerMsftProvider -Repository PSGallery -Force
    Write-Host "🐳 Docker Engine yükleniyor..." -ForegroundColor Yellow
    Install-Package -Name docker -ProviderName DockerMsftProvider -Force

    Write-Host "🚀 Docker Service başlatılıyor..." -ForegroundColor Yellow
    Start-Service Docker
    Set-Service -Name Docker -StartupType Automatic

    Write-Host "✅ Docker Engine kurulumu tamamlandı!" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ Standart kurulum başarısız, alternatif yöntem kullanılıyor..." -ForegroundColor Yellow

    # ALTERNATIF DOCKER KURULUMU
    Write-Host "📥 Docker'ı manuel olarak indiriliyor..." -ForegroundColor Cyan

    # Docker zip dosyasını indir
    $dockerUrl = "https://download.docker.com/win/static/stable/x86_64/docker-20.10.21.zip"
    $downloadPath = "C:\temp\docker.zip"

    # Temp klasörü oluştur
    if (!(Test-Path "C:\temp")) {
        New-Item -ItemType Directory -Path "C:\temp" -Force
    }

    try {
        # Docker'ı indir
        Write-Host "⬇️ Docker indiriliyor..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $dockerUrl -OutFile $downloadPath -UseBasicParsing

        # Zip'i çıkart
        Write-Host "📦 Docker çıkartılıyor..." -ForegroundColor Yellow
        Expand-Archive -Path $downloadPath -DestinationPath "C:\Program Files" -Force -ErrorAction SilentlyContinue

        # PATH'e ekle
        Write-Host "🔧 PATH güncelleniyor..." -ForegroundColor Yellow
        $env:PATH += ";C:\Program Files\Docker"
        [Environment]::SetEnvironmentVariable("PATH", $env:PATH, [EnvironmentVariableTarget]::Machine)

        # Docker daemon'u service olarak kaydet
        Write-Host "⚙️ Docker service kaydediliyor..." -ForegroundColor Yellow
        & "C:\Program Files\Docker\dockerd.exe" --register-service 2>$null

        # Service'i başlat
        Write-Host "🚀 Docker service başlatılıyor..." -ForegroundColor Yellow
        Start-Service Docker
        Set-Service -Name Docker -StartupType Automatic

        Write-Host "✅ Docker başarıyla kuruldu!" -ForegroundColor Green

        # Temizlik
        Remove-Item $downloadPath -Force -ErrorAction SilentlyContinue

    } catch {
        Write-Error "❌ Docker kurulumu başarısız: $_"
        Write-Host "💡 Manuel kurulum gerekebilir" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "⚠️ Sunucuyu yeniden başlatmanız gerekiyor!" -ForegroundColor Red
Write-Host "💡 Restart-Computer komutunu çalıştırın" -ForegroundColor Cyan

🔥 ADIM 1.4 - RESTART SONRASI DOĞRULAMA VE PATH DÜZELTME:
Restart yaptıktan sonra aşağıdaki kodu çalıştırın:

```powershell
# RESTART SONRASI DOCKER DOĞRULAMA VE PATH DÜZELTME
Write-Host "🔍 Docker kurulum doğrulaması yapılıyor..." -ForegroundColor Green

# PATH kontrolü ve düzeltme
Write-Host "🔧 Docker PATH kontrolü yapılıyor..." -ForegroundColor Yellow
$dockerPath = "C:\Program Files\Docker"
$currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::Machine)

if ($currentPath -notlike "*$dockerPath*") {
    Write-Host "⚠️ Docker PATH'e ekleniyor..." -ForegroundColor Yellow
    $newPath = $currentPath + ";" + $dockerPath
    [Environment]::SetEnvironmentVariable("PATH", $newPath, [EnvironmentVariableTarget]::Machine)
    $env:PATH = $newPath
    Write-Host "✅ Docker PATH'e eklendi" -ForegroundColor Green
} else {
    Write-Host "✅ Docker zaten PATH'de mevcut" -ForegroundColor Green
    # Mevcut session için PATH'i yenile
    $env:PATH = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::Machine)
}

# Docker Service kontrolü
Write-Host "🔍 Docker Service durumu:" -ForegroundColor Cyan
$dockerService = Get-Service -Name Docker -ErrorAction SilentlyContinue
if ($dockerService) {
    Write-Host "   Service Status: $($dockerService.Status)" -ForegroundColor White
    if ($dockerService.Status -ne "Running") {
        Write-Host "🚀 Docker Service başlatılıyor..." -ForegroundColor Yellow
        Start-Service Docker
    }
} else {
    Write-Host "❌ Docker Service bulunamadı!" -ForegroundColor Red
}

# Docker version kontrol
Write-Host "📋 Docker version:" -ForegroundColor Cyan
try {
    & "C:\Program Files\Docker\docker.exe" version
    Write-Host "✅ Docker version başarılı" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker version hatası: $_" -ForegroundColor Red
}

# Docker info kontrol
Write-Host "📊 Docker info:" -ForegroundColor Cyan
try {
    & "C:\Program Files\Docker\docker.exe" info
    Write-Host "✅ Docker info başarılı" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker info hatası: $_" -ForegroundColor Red
}

# Test container çalıştır
Write-Host "🧪 Test container çalıştırılıyor..." -ForegroundColor Cyan
try {
    & "C:\Program Files\Docker\docker.exe" run hello-world
    Write-Host "✅ Test container başarılı" -ForegroundColor Green
} catch {
    Write-Host "❌ Test container hatası: $_" -ForegroundColor Red
}

Write-Host "✅ Docker doğrulama tamamlandı! Adım 2'ye geçebilirsiniz." -ForegroundColor Green
```
```

================================================================
ADIM 2: PRODUCTION REDİS CONFIGURATION
================================================================

🔥 ADIM 2 - TEK SEFERDE KOPYALA YAPIŞTIIR:
Restart sonrası PowerShell'i Administrator olarak açın ve aşağıdaki kodu yapıştırın:

```powershell
# ADIM 2: REDIS CONFIGURATION - TEK SEFERDE ÇALIŞTIR
Write-Host "📁 Redis klasör yapısı oluşturuluyor..." -ForegroundColor Green

# 2.1 - Proje klasörü oluştur
mkdir C:\GymProject\Redis -Force
cd C:\GymProject\Redis
mkdir data -Force
mkdir logs -Force
mkdir config -Force
mkdir backup -Force
mkdir scripts -Force

Write-Host "✅ Klasör yapısı oluşturuldu!" -ForegroundColor Green
```

# ADIM 2.2 - REDİS CONFIG DOSYASI OLUŞTUR
Write-Host "⚙️ Redis config dosyası oluşturuluyor..." -ForegroundColor Yellow

# REDIS.CONF DOSYASI OLUŞTUR
$redisConfig = @"
# Redis Production Configuration for GymKod
# Windows Server 2019 - 4GB RAM, 2 CPU

# Network
bind 0.0.0.0
port 6379
protected-mode yes
tcp-keepalive 300
timeout 300

# Authentication
requirepass GymKodProd2024Redis!Secure

# Memory Management (4GB RAM için optimize)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence (Production için güçlendirilmiş)
save 900 1
save 300 10
save 60 10000

# AOF Persistence
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Performance Tuning
tcp-backlog 511
databases 16

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_b835729c9c13a228"

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128
"@

$redisConfig | Out-File -FilePath "C:\GymProject\Redis\config\redis.conf" -Encoding UTF8
Write-Host "✅ Redis configuration dosyası oluşturuldu!" -ForegroundColor Green

# ADIM 2.3 - DOCKER COMPOSE DOSYASI OLUŞTUR
Write-Host "🐳 Docker Compose dosyası oluşturuluyor..." -ForegroundColor Yellow

# DOCKER-COMPOSE.PROD.YML DOSYASI OLUŞTUR
$dockerCompose = @"
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - C:\GymProject\Redis\data:/data
      - C:\GymProject\Redis\config\redis.conf:/usr/local/etc/redis/redis.conf
      - C:\GymProject\Redis\logs:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=GymKodProd2024Redis!Secure
    networks:
      - gymkod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "GymKodProd2024Redis!Secure", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.5'

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge
"@

$dockerCompose | Out-File -FilePath "C:\GymProject\Redis\docker-compose.prod.yml" -Encoding UTF8
Write-Host "✅ Docker Compose dosyası oluşturuldu!" -ForegroundColor Green

================================================================
ADIM 3: REDİS CONTAINER DEPLOYMENT
================================================================

🔥 ADIM 3 - TEK SEFERDE KOPYALA YAPIŞTIIR:
Aşağıdaki kodu kopyalayıp yapıştırın:

# ADIM 3: REDIS NATIVE WINDOWS KURULUMU - TEK SEFERDE ÇALIŞTIR
# Not: Windows Server 2019'da Linux container sorunu nedeniyle native kurulum yapıyoruz
Write-Host "🚀 Redis Windows native kurulumu başlatılıyor..." -ForegroundColor Green

cd C:\GymProject\Redis

# 3.1 - Redis Windows binary indir
Write-Host "📥 Redis Windows binary indiriliyor..." -ForegroundColor Yellow
$redisUrl = "https://github.com/tporadowski/redis/releases/download/v********/Redis-x64-********.zip"
$redisZip = "C:\GymProject\Redis\redis-windows.zip"
$redisPath = "C:\GymProject\Redis\redis-server"

try {
    # Redis'i indir
    Invoke-WebRequest -Uri $redisUrl -OutFile $redisZip -UseBasicParsing
    Write-Host "✅ Redis indirildi" -ForegroundColor Green

    # Zip'i çıkart
    if (Test-Path $redisPath) {
        Remove-Item $redisPath -Recurse -Force
    }
    Expand-Archive -Path $redisZip -DestinationPath $redisPath -Force
    Write-Host "✅ Redis çıkartıldı" -ForegroundColor Green

    # Temizlik
    Remove-Item $redisZip -Force

} catch {
    Write-Host "❌ Redis indirme hatası: $_" -ForegroundColor Red
    exit 1
}

# 3.2 - Redis config dosyasını Windows için düzenle
Write-Host "⚙️ Redis config Windows için düzenleniyor..." -ForegroundColor Yellow
$windowsRedisConfig = @"
# Redis Production Configuration for GymKod - Windows Native
# Windows Server 2019 - 4GB RAM, 2 CPU

# Network
bind 127.0.0.1
port 6379
tcp-keepalive 300
timeout 300

# Authentication
requirepass GymKodProd2024Redis!Secure

# Memory Management (4GB RAM için optimize)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence (Production için güçlendirilmiş)
save 900 1
save 300 10
save 60 10000

# AOF Persistence
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile C:\GymProject\Redis\logs\redis-server.log

# Performance Tuning
tcp-backlog 511
databases 16

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_b835729c9c13a228"

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Windows specific
dir C:\GymProject\Redis\data
"@

$windowsRedisConfig | Out-File -FilePath "C:\GymProject\Redis\config\redis-windows.conf" -Encoding UTF8
Write-Host "✅ Windows Redis config oluşturuldu" -ForegroundColor Green

# 3.3 - Redis Service olarak kur ve başlat
Write-Host "� Redis Service olarak kuruluyor..." -ForegroundColor Yellow
$redisExe = "$redisPath\redis-server.exe"
$redisConf = "C:\GymProject\Redis\config\redis-windows.conf"

try {
    # Mevcut Redis service'i durdur (varsa)
    $existingService = Get-Service -Name "Redis" -ErrorAction SilentlyContinue
    if ($existingService) {
        Write-Host "⚠️ Mevcut Redis service durduruluyor..." -ForegroundColor Yellow
        Stop-Service -Name "Redis" -Force
        & sc.exe delete "Redis"
        Start-Sleep 3
    }

    # Redis'i service olarak kur
    Write-Host "📦 Redis service kuruluyor..." -ForegroundColor Yellow
    & $redisExe --service-install --service-name "Redis" --port 6379 --loglevel notice --logfile "C:\GymProject\Redis\logs\redis-server.log"

    # Service'i başlat
    Write-Host "🚀 Redis service başlatılıyor..." -ForegroundColor Yellow
    Start-Service -Name "Redis"
    Set-Service -Name "Redis" -StartupType Automatic

    Write-Host "✅ Redis service kuruldu ve başlatıldı" -ForegroundColor Green

} catch {
    Write-Host "❌ Redis service kurulum hatası: $_" -ForegroundColor Red
    Write-Host "🔄 Manuel başlatma deneniyor..." -ForegroundColor Yellow

    # Manuel başlatma
    try {
        Start-Process -FilePath $redisExe -ArgumentList $redisConf -WindowStyle Hidden
        Write-Host "✅ Redis manuel olarak başlatıldı" -ForegroundColor Green
    } catch {
        Write-Host "❌ Redis manuel başlatma hatası: $_" -ForegroundColor Red
    }
}

Write-Host "⏳ Redis'in başlaması bekleniyor..." -ForegroundColor Yellow
Start-Sleep 10

# 3.4 - Redis durumunu kontrol et
Write-Host "📊 Redis durumu kontrol ediliyor..." -ForegroundColor Cyan
try {
    $redisService = Get-Service -Name "Redis" -ErrorAction SilentlyContinue
    if ($redisService) {
        Write-Host "   Service Status: $($redisService.Status)" -ForegroundColor White
        if ($redisService.Status -eq "Running") {
            Write-Host "✅ Redis Service çalışıyor" -ForegroundColor Green
        } else {
            Write-Host "❌ Redis Service çalışmıyor" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️ Redis Service bulunamadı, process kontrol ediliyor..." -ForegroundColor Yellow
        $redisProcess = Get-Process -Name "redis-server" -ErrorAction SilentlyContinue
        if ($redisProcess) {
            Write-Host "✅ Redis process çalışıyor" -ForegroundColor Green
        } else {
            Write-Host "❌ Redis process bulunamadı" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ Redis durum kontrolü hatası: $_" -ForegroundColor Red
}

Write-Host "✅ Redis Windows native kurulumu tamamlandı!" -ForegroundColor Green

# ADIM 3.2 - REDIS BAĞLANTI VE TEST - TEK SEFERDE ÇALIŞTIR
Write-Host "🔍 Redis bağlantı testi yapılıyor..." -ForegroundColor Green

# Redis CLI path'i ayarla
$redisCliPath = "C:\GymProject\Redis\redis-server\redis-cli.exe"

# 3.2.1 - Ping testi
Write-Host "📡 Redis Ping testi..." -ForegroundColor Cyan
try {
    $pingResult = & $redisCliPath -a "GymKodProd2024Redis!Secure" ping
    Write-Host "   Ping sonucu: $pingResult" -ForegroundColor White
    if ($pingResult -eq "PONG") {
        Write-Host "✅ Redis bağlantısı başarılı" -ForegroundColor Green
    } else {
        Write-Host "❌ Redis bağlantısı başarısız" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Ping testi hatası: $_" -ForegroundColor Red
}

# 3.2.2 - Test verisi kaydet ve oku
Write-Host "💾 Test verisi kaydediliyor..." -ForegroundColor Cyan
try {
    & $redisCliPath -a "GymKodProd2024Redis!Secure" set "test:gymkod" "Hello GymKod Production!"
    $testResult = & $redisCliPath -a "GymKodProd2024Redis!Secure" get "test:gymkod"
    Write-Host "   Test verisi: $testResult" -ForegroundColor White

    if ($testResult -eq "Hello GymKod Production!") {
        Write-Host "✅ Veri kaydetme/okuma başarılı" -ForegroundColor Green
    } else {
        Write-Host "❌ Veri kaydetme/okuma başarısız" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Test verisi hatası: $_" -ForegroundColor Red
}

# 3.2.3 - Memory bilgisi
Write-Host "💾 Redis memory bilgisi:" -ForegroundColor Cyan
try {
    $memoryInfo = & $redisCliPath -a "GymKodProd2024Redis!Secure" info memory
    $usedMemory = ($memoryInfo | Select-String "used_memory_human").ToString().Split(':')[1].Trim()
    $maxMemory = ($memoryInfo | Select-String "maxmemory_human").ToString().Split(':')[1].Trim()

    Write-Host "   Kullanılan Memory: $usedMemory" -ForegroundColor White
    Write-Host "   Maksimum Memory: $maxMemory" -ForegroundColor White
} catch {
    Write-Host "❌ Memory bilgisi alınamadı: $_" -ForegroundColor Red
}

# 3.2.4 - Redis info
Write-Host "📊 Redis genel bilgiler:" -ForegroundColor Cyan
try {
    $serverInfo = & $redisCliPath -a "GymKodProd2024Redis!Secure" info server
    $version = ($serverInfo | Select-String "redis_version").ToString().Split(':')[1].Trim()
    $uptime = ($serverInfo | Select-String "uptime_in_seconds").ToString().Split(':')[1].Trim()

    Write-Host "   Redis Version: $version" -ForegroundColor White
    Write-Host "   Uptime: $uptime saniye" -ForegroundColor White
} catch {
    Write-Host "❌ Server bilgisi alınamadı: $_" -ForegroundColor Red
}

# 3.2.5 - Multi-tenant test
Write-Host "🏢 Multi-tenant cache testi..." -ForegroundColor Cyan
try {
    # Farklı company'ler için test verileri
    & $redisCliPath -a "GymKodProd2024Redis!Secure" set "gym:1:member:123" "Ahmet Yılmaz"
    & $redisCliPath -a "GymKodProd2024Redis!Secure" set "gym:2:member:123" "Mehmet Demir"

    $company1Data = & $redisCliPath -a "GymKodProd2024Redis!Secure" get "gym:1:member:123"
    $company2Data = & $redisCliPath -a "GymKodProd2024Redis!Secure" get "gym:2:member:123"

    Write-Host "   Company 1 Data: $company1Data" -ForegroundColor White
    Write-Host "   Company 2 Data: $company2Data" -ForegroundColor White

    if ($company1Data -ne $company2Data) {
        Write-Host "✅ Multi-tenant isolation çalışıyor" -ForegroundColor Green
    } else {
        Write-Host "❌ Multi-tenant isolation sorunu" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Multi-tenant test hatası: $_" -ForegroundColor Red
}

Write-Host "✅ Redis bağlantı ve test tamamlandı!" -ForegroundColor Green
Write-Host "🎯 .NET Connection String:" -ForegroundColor Yellow
Write-Host "   localhost:6379,password=GymKodProd2024Redis!Secure,abortConnect=false,connectTimeout=10000,syncTimeout=10000,connectRetry=3,keepAlive=60,ssl=false" -ForegroundColor Cyan
```

================================================================
ADIM 4: WINDOWS FIREWALL VE GÜVENLİK
================================================================

🔥 ADIM 4 - TEK SEFERDE KOPYALA YAPIŞTIIR:
Aşağıdaki kodu kopyalayıp yapıştırın:

```powershell
# ADIM 4: FIREWALL VE GÜVENLİK - TEK SEFERDE ÇALIŞTIR
Write-Host "🔥 Firewall kuralları ekleniyor..." -ForegroundColor Green

# 4.1 - Firewall kuralları
try {
    # Redis port'unu aç (sadece local network)
    New-NetFirewallRule -DisplayName "GymKod Redis Server" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -RemoteAddress LocalSubnet -ErrorAction Stop
    Write-Host "✅ Redis firewall kuralı eklendi" -ForegroundColor Green

    # Docker için gerekli portlar
    New-NetFirewallRule -DisplayName "GymKod Docker API" -Direction Inbound -Protocol TCP -LocalPort 2375,2376 -Action Allow -RemoteAddress LocalSubnet -ErrorAction Stop
    Write-Host "✅ Docker firewall kuralları eklendi" -ForegroundColor Green
}
catch {
    Write-Warning "⚠️ Firewall kuralı eklenemedi: $_"
}

# 4.2 - Güvenlik sertleştirme
Write-Host "🔒 Klasör izinleri ayarlanıyor..." -ForegroundColor Yellow

try {
    # Redis klasör izinleri
    icacls "C:\GymProject\Redis" /grant "Administrators:(OI)(CI)F" /T /Q
    icacls "C:\GymProject\Redis" /remove "Users" /T /Q

    # Log dosyası izinleri
    icacls "C:\GymProject\Redis\logs" /grant "Everyone:(OI)(CI)W" /T /Q

    Write-Host "✅ Klasör izinleri ayarlandı" -ForegroundColor Green
}
catch {
    Write-Warning "⚠️ Klasör izinleri ayarlanamadı: $_"
}

Write-Host "✅ Güvenlik ayarları tamamlandı!" -ForegroundColor Green
```

================================================================
ADIM 5: MONITORING VE BACKUP SCRIPTLERI
================================================================

🔥 ADIM 5 - TEK SEFERDE KOPYALA YAPIŞTIIR:
Aşağıdaki kodu kopyalayıp yapıştırın:

```powershell
# ADIM 5: MONITORING VE BACKUP SCRIPTLERI - TEK SEFERDE ÇALIŞTIR
Write-Host "📜 Utility scripts oluşturuluyor..." -ForegroundColor Green

# 5.1 - Health Check Script oluştur
$healthCheckScript = @"
# Redis Health Check Script
`$redisPassword = "GymKodProd2024Redis!Secure"
`$containerName = "gymkod-redis-prod"

try {
    `$result = docker exec `$containerName redis-cli -a `$redisPassword ping
    if (`$result -eq "PONG") {
        Write-Host "✅ Redis is healthy" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "❌ Redis ping failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Redis connection error: `$_" -ForegroundColor Red
    exit 1
}
"@

$healthCheckScript | Out-File -FilePath "C:\GymProject\Redis\scripts\health_check.ps1" -Encoding UTF8
Write-Host "✅ Health check script oluşturuldu" -ForegroundColor Green

# 5.2 - Backup Script oluştur
$backupScript = @"
# Redis Backup Script
`$backupDir = "C:\GymProject\Redis\backup"
`$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
`$backupFile = "`$backupDir\redis_backup_`$timestamp.rdb"

Write-Host "🔄 Redis backup başlatılıyor..." -ForegroundColor Yellow

# Backup klasörü oluştur
if (!(Test-Path `$backupDir)) {
    New-Item -ItemType Directory -Path `$backupDir -Force
}

try {
    # Redis BGSAVE komutu
    docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure BGSAVE
    Start-Sleep 5

    # RDB dosyasını kopyala
    docker cp gymkod-redis-prod:/data/dump.rdb `$backupFile

    Write-Host "✅ Backup completed: `$backupFile" -ForegroundColor Green

    # 7 günden eski backup'ları sil
    Get-ChildItem `$backupDir -Filter "*.rdb" | Where-Object {`$_.CreationTime -lt (Get-Date).AddDays(-7)} | Remove-Item -Force
    Write-Host "🧹 Eski backup'lar temizlendi" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Backup hatası: `$_" -ForegroundColor Red
    exit 1
}
"@

$backupScript | Out-File -FilePath "C:\GymProject\Redis\scripts\backup.ps1" -Encoding UTF8
Write-Host "✅ Backup script oluşturuldu" -ForegroundColor Green

Write-Host "✅ Tüm utility scripts oluşturuldu!" -ForegroundColor Green
```

================================================================
ADIM 6: OTOMATIK BAŞLATMA VE SERVİS YÖNETİMİ
================================================================

🔥 ADIM 6 - TEK SEFERDE KOPYALA YAPIŞTIIR:
Aşağıdaki kodu kopyalayıp yapıştırın:

```powershell
# ADIM 6: OTOMATIK BAŞLATMA VE SERVİS YÖNETİMİ - TEK SEFERDE ÇALIŞTIR
Write-Host "⏰ Scheduled tasks oluşturuluyor..." -ForegroundColor Green

try {
    # 6.1 - Redis otomatik başlatma task'ı oluştur
    Write-Host "🚀 Redis otomatik başlatma task'ı oluşturuluyor..." -ForegroundColor Yellow

    $action = New-ScheduledTaskAction -Execute "docker-compose" -Argument "-f C:\GymProject\Redis\docker-compose.prod.yml up -d" -WorkingDirectory "C:\GymProject\Redis"
    $trigger = New-ScheduledTaskTrigger -AtStartup
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

    Register-ScheduledTask -TaskName "GymKod Redis Startup" -Action $action -Trigger $trigger -Settings $settings -User "SYSTEM" -Force
    Write-Host "✅ Redis startup task oluşturuldu" -ForegroundColor Green

    # 6.2 - Günlük backup task'ı oluştur
    Write-Host "💾 Günlük backup task'ı oluşturuluyor..." -ForegroundColor Yellow

    $backupAction = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GymProject\Redis\scripts\backup.ps1"
    $backupTrigger = New-ScheduledTaskTrigger -Daily -At "02:00"

    Register-ScheduledTask -TaskName "GymKod Redis Backup" -Action $backupAction -Trigger $backupTrigger -User "SYSTEM" -Force
    Write-Host "✅ Backup task oluşturuldu" -ForegroundColor Green

    # Task'ları listele
    Write-Host "📋 Oluşturulan task'lar:" -ForegroundColor Cyan
    Get-ScheduledTask | Where-Object {$_.TaskName -like "*GymKod*"} | Select-Object TaskName, State

    Write-Host "✅ Tüm scheduled tasks oluşturuldu!" -ForegroundColor Green
}
catch {
    Write-Warning "⚠️ Scheduled task oluşturma hatası: $_"
}
```

================================================================
ADIM 7: .NET UYGULAMASI PRODUCTION CONNECTION
================================================================

🔥 ADIM 7 - FINAL TEST VE .NET BAĞLANTISI:
Aşağıdaki kodu kopyalayıp yapıştırın:

```powershell
# ADIM 7: FINAL TEST VE ÖZET - TEK SEFERDE ÇALIŞTIR
Write-Host "🎉 Redis kurulumu tamamlandı! Final test yapılıyor..." -ForegroundColor Green

# 7.1 - Sistem durumu kontrolü
Write-Host "📊 Sistem durumu:" -ForegroundColor Cyan
Write-Host "   Docker Service: " -NoNewline
if ((Get-Service Docker).Status -eq "Running") { Write-Host "✅ Çalışıyor" -ForegroundColor Green } else { Write-Host "❌ Durmuş" -ForegroundColor Red }

Write-Host "   Redis Container: " -NoNewline
$containerStatus = docker ps --filter "name=gymkod-redis-prod" --format "{{.Status}}"
if ($containerStatus -like "*Up*") { Write-Host "✅ Çalışıyor" -ForegroundColor Green } else { Write-Host "❌ Durmuş" -ForegroundColor Red }

# 7.2 - Redis bağlantı testi
Write-Host "🔍 Redis bağlantı testi:" -ForegroundColor Cyan
$pingResult = docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure ping 2>$null
Write-Host "   Ping: $pingResult" -ForegroundColor $(if($pingResult -eq "PONG") {"Green"} else {"Red"})

# 7.3 - Memory kullanımı
Write-Host "💾 Memory kullanımı:" -ForegroundColor Cyan
$memoryInfo = docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure info memory | Select-String "used_memory_human"
Write-Host "   $memoryInfo" -ForegroundColor White

# 7.4 - Backup testi
Write-Host "💾 Backup testi yapılıyor..." -ForegroundColor Cyan
& "C:\GymProject\Redis\scripts\backup.ps1"

# 7.5 - Health check testi
Write-Host "🏥 Health check testi:" -ForegroundColor Cyan
& "C:\GymProject\Redis\scripts\health_check.ps1"

Write-Host ""
Write-Host "🎯 KURULUM BAŞARILI! ÖNEMLİ BİLGİLER:" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host "🔗 Redis Connection String (.NET için):" -ForegroundColor Yellow
Write-Host "   localhost:6379,password=GymKodProd2024Redis!Secure,abortConnect=false,connectTimeout=10000,syncTimeout=10000,connectRetry=3,keepAlive=60,ssl=false" -ForegroundColor White
Write-Host ""
Write-Host "📂 Önemli Dosyalar:" -ForegroundColor Yellow
Write-Host "   Config: C:\GymProject\Redis\config\redis.conf" -ForegroundColor White
Write-Host "   Compose: C:\GymProject\Redis\docker-compose.prod.yml" -ForegroundColor White
Write-Host "   Health Check: C:\GymProject\Redis\scripts\health_check.ps1" -ForegroundColor White
Write-Host "   Backup: C:\GymProject\Redis\scripts\backup.ps1" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Yönetim Komutları:" -ForegroundColor Yellow
Write-Host "   Container Logs: docker logs gymkod-redis-prod" -ForegroundColor White
Write-Host "   Redis CLI: docker exec -it gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure" -ForegroundColor White
Write-Host "   Container Restart: docker restart gymkod-redis-prod" -ForegroundColor White
Write-Host ""
Write-Host "✅ .NET uygulamanızı test edebilirsiniz!" -ForegroundColor Green
```

================================================================
SONRAKI ADIMLAR (PROMPT 8-10)
================================================================

✅ TAMAMLANAN: Redis Production Kurulumu
🔄 SONRAKI: Performance Optimization ve Load Testing
🔄 SONRAKI: Cache Reliability ve Circuit Breaker
🔄 SONRAKI: Security Hardening ve Final Deployment

Bu rehberi takip ederek Windows Server 2019'da production-ready Redis kurulumu yapabilirsiniz.

================================================================
ADIM 8: TROUBLESHOOTING VE SORUN GİDERME
================================================================

8.1 YAYGIN SORUNLAR VE ÇÖZÜMLERİ:

SORUN 1: Docker Engine kurulumu başarısız
ÇÖZÜM:
```powershell
# Windows Update'leri kontrol et
Get-WindowsUpdate
Install-WindowsUpdate -AcceptAll -AutoReboot

# .NET Framework 4.8 kurulu mu kontrol et
Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release
```

SORUN 2: Container başlatılamıyor
ÇÖZÜM:
```powershell
# Docker service durumunu kontrol et
Get-Service Docker
Restart-Service Docker

# Container loglarını incele
docker logs gymkod-redis-prod --tail 50

# Port kullanımını kontrol et
netstat -an | findstr :6379
```

SORUN 3: Redis bağlantı hatası
ÇÖZÜM:
```powershell
# Redis container içine gir
docker exec -it gymkod-redis-prod sh

# Redis config'i kontrol et
redis-cli -a GymKodProd2024Redis!Secure CONFIG GET "*"

# Memory kullanımını kontrol et
redis-cli -a GymKodProd2024Redis!Secure INFO memory
```

8.2 PERFORMANCE MONİTORİNG:
```powershell
# Redis stats monitoring script
# C:\GymKod\Redis\scripts\monitor.ps1
$password = "GymKodProd2024Redis!Secure"

while ($true) {
    $info = docker exec gymkod-redis-prod redis-cli -a $password INFO stats
    $memory = docker exec gymkod-redis-prod redis-cli -a $password INFO memory

    Write-Host "=== Redis Stats $(Get-Date) ===" -ForegroundColor Yellow
    Write-Host $info
    Write-Host $memory

    Start-Sleep 60
}
```

================================================================
ADIM 9: PRODUCTION DEPLOYMENT CHECKLİST
================================================================

9.1 KURULUM ÖNCESİ KONTROLLER:
□ Windows Server 2019 güncel mi?
□ 4GB RAM mevcut mu?
□ Disk alanı yeterli mi? (min 20GB)
□ Network bağlantısı stabil mi?
□ Firewall kuralları hazır mı?

9.2 KURULUM SONRASI KONTROLLER:
□ Docker Engine çalışıyor mu?
□ Redis container ayakta mı?
□ Redis AUTH çalışıyor mu?
□ Backup script'i çalışıyor mu?
□ Health check çalışıyor mu?
□ .NET app bağlanabiliyor mu?

9.3 GÜVENLİK KONTROLLERİ:
□ Redis password güçlü mü?
□ Firewall kuralları aktif mi?
□ Klasör izinleri doğru mu?
□ Tehlikeli komutlar disable mi?
□ Log dosyaları korunuyor mu?

================================================================
ADIM 10: KAPASITEYI ARTTIRMA PLANI
================================================================

10.1 SUNUCU KAPASITESI ARTTIRMA (İLERDE):
Mevcut: 4GB RAM, 2 CPU → 1 salon 100 üye
Hedef: 16GB RAM, 8 CPU → 100 salon 10K üye

REDIS CONFIG DEĞİŞİKLİKLERİ:
```conf
# 16GB RAM için
maxmemory 8gb
maxmemory-policy allkeys-lru

# Performance tuning
tcp-backlog 2048
databases 32

# Connection limits
maxclients 10000
```

DOCKER COMPOSE DEĞİŞİKLİKLERİ:
```yaml
deploy:
  resources:
    limits:
      memory: 8G
      cpus: '4.0'
    reservations:
      memory: 4G
      cpus: '2.0'
```

10.2 CLUSTER KURULUMU (100+ SALON İÇİN):
```yaml
# Redis Cluster için 3 node
services:
  redis-node-1:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes-6379.conf --cluster-node-timeout 5000 --appendonly yes --port 6379

  redis-node-2:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes-6380.conf --cluster-node-timeout 5000 --appendonly yes --port 6380

  redis-node-3:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes-6381.conf --cluster-node-timeout 5000 --appendonly yes --port 6381
```

================================================================
ADIM 11: MAINTENANCE VE GÜNCELLEME
================================================================

11.1 REDIS GÜNCELLEME PROCEDURE:
```powershell
# 1. Backup al
C:\GymKod\Redis\scripts\backup.ps1

# 2. Container'ı durdur
docker-compose -f C:\GymKod\Redis\docker-compose.prod.yml down

# 3. Image'i güncelle
docker pull redis:7-alpine

# 4. Container'ı yeniden başlat
docker-compose -f C:\GymKod\Redis\docker-compose.prod.yml up -d

# 5. Health check
C:\GymKod\Redis\scripts\health_check.ps1
```

11.2 WEEKLY MAINTENANCE SCRIPT:
```powershell
# C:\GymKod\Redis\scripts\weekly_maintenance.ps1
Write-Host "🔧 Weekly Redis Maintenance Started" -ForegroundColor Blue

# 1. Backup
& "C:\GymKod\Redis\scripts\backup.ps1"

# 2. Log rotation
$logFile = "C:\GymKod\Redis\logs\redis-server.log"
if ((Get-Item $logFile).Length -gt 100MB) {
    Move-Item $logFile "$logFile.old"
    docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure CONFIG SET logfile /var/log/redis/redis-server.log
}

# 3. Memory optimization
docker exec gymkod-redis-prod redis-cli -a GymKodProd2024Redis!Secure MEMORY PURGE

# 4. Health report
& "C:\GymKod\Redis\scripts\health_check.ps1"

Write-Host "✅ Weekly Maintenance Completed" -ForegroundColor Green
```

================================================================
ADIM 12: EMERGENCY RECOVERY PLAN
================================================================

12.1 REDIS CRASH RECOVERY:
```powershell
# 1. Container durumunu kontrol et
docker ps -a | findstr gymkod-redis

# 2. Container restart
docker restart gymkod-redis-prod

# 3. Eğer restart çalışmazsa
docker-compose -f C:\GymKod\Redis\docker-compose.prod.yml down
docker-compose -f C:\GymKod\Redis\docker-compose.prod.yml up -d

# 4. Data corruption varsa backup'tan restore
docker cp C:\GymKod\Redis\backup\redis_backup_YYYYMMDD_HHMMSS.rdb gymkod-redis-prod:/data/dump.rdb
docker restart gymkod-redis-prod
```

12.2 DISASTER RECOVERY:
```powershell
# Tam sistem çökmesi durumunda
# 1. Yeni sunucuda Docker kurulumu
# 2. Redis config dosyalarını kopyala
# 3. Son backup'ı restore et
# 4. .NET uygulamasını yeni sunucuya yönlendir
```

================================================================
SONUÇ VE İLETİŞİM
================================================================

✅ KURULUM TAMAMLANDI:
- Windows Server 2019'da Docker Engine
- Production Redis Container
- Monitoring ve Backup Scripts
- Security Hardening
- Auto-restart Configuration

📞 DESTEK GEREKTİĞİNDE:
1. Redis loglarını kontrol et: docker logs gymkod-redis-prod
2. Health check çalıştır: C:\GymKod\Redis\scripts\health_check.ps1
3. Backup'ları kontrol et: C:\GymKod\Redis\backup\
4. .NET uygulaması connection string'ini kontrol et

🚀 SONRAKI ADIMLAR:
- Prompt 8: Performance Optimization ve Load Testing
- Prompt 9: Cache Reliability ve Circuit Breaker
- Prompt 10: Security Hardening ve Final Deployment

Bu rehber ile Windows Server 2019'da production-ready Redis kurulumu tamamlanmıştır.
